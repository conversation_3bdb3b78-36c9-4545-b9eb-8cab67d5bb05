/**
 * Learn more about light and dark modes:
 * https://docs.expo.dev/guides/color-schemes/
 */
import { Colors } from '@/constants/Colors';
import { useTheme } from '@/context/ThemeContext';

type ColorName = Exclude<keyof typeof Colors.lightBlue, 'linearGradient'>;

export function useThemeColor(props: { light?: string; dark?: string } = {}, colorName: ColorName) {
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useThemeColor: currentTheme is invalid, falling back to lightBlue');
    const fallbackTheme = 'lightBlue';
    const isLightTheme = true;
    const themeType = isLightTheme ? 'light' : 'dark';
    const colorFromProps = props[themeType];

    if (colorFromProps) {
      return colorFromProps;
    }
    return Colors[fallbackTheme][colorName];
  }

  // Determine if current theme is light or dark for props fallback
  const isLightTheme = currentTheme.startsWith('light');
  const themeType = isLightTheme ? 'light' : 'dark';
  const colorFromProps = props[themeType];

  if (colorFromProps) {
    return colorFromProps;
  } else {
    // Use the specific theme colors
    return Colors[currentTheme][colorName];
  }
}

// New hook to get colors directly from current theme
export function useCurrentThemeColors() {
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useCurrentThemeColors: currentTheme is invalid, falling back to lightBlue');
    return Colors.lightBlue;
  }

  return Colors[currentTheme];
}

// Helper hook to get a specific color from current theme
export function useThemeColorDirect(colorName: ColorName) {
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useThemeColorDirect: currentTheme is invalid, falling back to lightBlue');
    return Colors.lightBlue[colorName];
  }

  return Colors[currentTheme][colorName];
}

// Hook to get linear gradient colors from current theme
export function useThemeLinearGradient() {
  const { currentTheme } = useTheme();

  // Safety check for currentTheme
  if (!currentTheme || !Colors[currentTheme]) {
    console.warn('useThemeLinearGradient: currentTheme is invalid, falling back to lightBlue');
    return Colors.lightBlue.linearGradient;
  }

  return Colors[currentTheme].linearGradient;
}

// Legacy hook for backward compatibility - returns object with theme properties
export function useThemeColorLegacy() {
  const colors = useCurrentThemeColors();
  const { currentTheme, setTheme } = useTheme();

  // Return object that matches the old API
  return {
    theme: colors.primary,
    text: colors.text,
    backgroundPrimary: colors.background,
    backgroundSecondary: colors.secondary,
    muted: colors.tabIconDefault,
    tint: colors.primary,
    icon: colors.icon,
    border: colors.border,
    tabIconDefault: colors.tabIconDefault,
    style: currentTheme.startsWith('light') ? 'light' : 'dark',
    allColors: {
      light: colors,
      dark: colors,
    },
    toggleTheme: () => {
      // Simple toggle between light and dark variants of current theme
      if (currentTheme.startsWith('light')) {
        const darkVariant = currentTheme.replace('light', 'dark') as any;
        setTheme(darkVariant);
      } else if (currentTheme.startsWith('dark')) {
        const lightVariant = currentTheme.replace('dark', 'light') as any;
        setTheme(lightVariant);
      }
    },
  };
}
