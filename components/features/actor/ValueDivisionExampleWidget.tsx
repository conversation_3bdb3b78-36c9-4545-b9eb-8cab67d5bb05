import React from 'react';

import { View } from 'react-native';

import { Text } from '@/components/base';
import { useCurrentThemeColors, useThemeColorLegacy } from '@/hooks/useThemeColor';

import ExpandableWidget from './ExpandableWidget';

// Theme-aware color palette for data visualization
const getSegmentColors = (colors: any) => [
  colors.primary, // Use primary theme color
  '#078566', // Green
  '#E97633', // Orange
  '#C84279', // Red/Pink
];

// Mock data for the example - similar to the image you showed
const getMockData = (colors: any) => ({
  totalValue: 6300000, // $6.3M
  segments: [
    { label: 'Technology', value: 2520000, percentage: 40, color: getSegmentColors(colors)[0] },
    { label: 'Healthcare', value: 1890000, percentage: 30, color: getSegmentColors(colors)[1] },
    { label: 'Finance', value: 1260000, percentage: 20, color: getSegmentColors(colors)[2] },
    { label: 'Energy', value: 630000, percentage: 10, color: getSegmentColors(colors)[3] },
  ],
  additionalDetails: [
    { category: 'Growth Stocks', value: 3780000, description: 'High-growth technology and healthcare companies' },
    { category: 'Value Stocks', value: 1890000, description: 'Undervalued financial and energy companies' },
    { category: 'Dividend Stocks', value: 630000, description: 'Stable dividend-paying companies' },
    { category: 'International', value: 1260000, description: 'Global diversification across markets' },
    { category: 'Bonds', value: 315000, description: 'Government and corporate bonds' },
    { category: 'Cash', value: 315000, description: 'Cash and cash equivalents' },
  ],
});

const CircularProgress = ({
  percentage,
  color,
  size = 120,
  totalValue,
}: {
  percentage: number;
  color: string;
  size?: number;
  totalValue: number;
}) => {
  const theme = useThemeColorLegacy();

  return (
    <View
      className="justify-center items-center rounded-full"
      style={{
        width: size,
        height: size,
        backgroundColor: color + '20',
        borderWidth: 8,
        borderColor: color,
      }}
    >
      <Text className="text-2xl font-bold" style={{ color: theme.text }}>
        ${(totalValue / 1000000).toFixed(1)}M
      </Text>
    </View>
  );
};

const PreviewContent = () => {
  const theme = useThemeColorLegacy();
  const colors = useCurrentThemeColors();
  const mockData = getMockData(colors);

  return (
    <View className="items-center">
      <CircularProgress percentage={100} color={theme.theme} totalValue={mockData.totalValue} />

      <View className="mt-4 w-full">
        {mockData.segments.slice(0, 2).map((segment: any, index: number) => (
          <View key={index} className="flex-row items-center justify-between py-2">
            <View className="flex-row items-center flex-1">
              <View className="w-3 h-3 rounded-full mr-3" style={{ backgroundColor: segment.color }} />
              <Text className="flex-1" style={{ color: theme.text }}>
                {segment.label}
              </Text>
            </View>
            <Text className="font-semibold" style={{ color: theme.text }}>
              {segment.percentage}%
            </Text>
          </View>
        ))}
        <Text className="text-center text-muted mt-2">+{mockData.segments.length - 2} more categories</Text>
      </View>
    </View>
  );
};

const FullContent = () => {
  const theme = useThemeColorLegacy();
  const colors = useCurrentThemeColors();
  const mockData = getMockData(colors);

  return (
    <View>
      {/* Main Chart */}
      <View className="items-center mb-6">
        <CircularProgress percentage={100} color={theme.theme} size={160} totalValue={mockData.totalValue} />
      </View>

      {/* All Segments */}
      <View className="mb-6">
        <Text h3 className="font-bold mb-4" style={{ color: theme.text }}>
          Portfolio Breakdown
        </Text>
        {mockData.segments.map((segment: any, index: number) => (
          <View
            key={index}
            className="flex-row items-center justify-between py-3 border-b"
            style={{ borderBottomColor: colors.border }}
          >
            <View className="flex-row items-center flex-1">
              <View className="w-4 h-4 rounded-full mr-3" style={{ backgroundColor: segment.color }} />
              <View className="flex-1">
                <Text className="font-medium" style={{ color: theme.text }}>
                  {segment.label}
                </Text>
                <Text className="text-sm text-muted">${(segment.value / 1000000).toFixed(1)}M</Text>
              </View>
            </View>
            <Text className="font-semibold text-lg" style={{ color: theme.text }}>
              {segment.percentage}%
            </Text>
          </View>
        ))}
      </View>

      {/* Additional Details */}
      <View>
        <Text h3 className="font-bold mb-4" style={{ color: theme.text }}>
          Detailed Analysis
        </Text>
        {mockData.additionalDetails.map((detail: any, index: number) => (
          <View key={index} className="py-3 border-b" style={{ borderBottomColor: colors.border }}>
            <View className="flex-row justify-between items-center mb-1">
              <Text className="font-medium" style={{ color: theme.text }}>
                {detail.category}
              </Text>
              <Text className="font-semibold" style={{ color: theme.text }}>
                ${(detail.value / 1000000).toFixed(1)}M
              </Text>
            </View>
            <Text className="text-sm text-muted">{detail.description}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

export default function ValueDivisionExampleWidget() {
  return (
    <ExpandableWidget
      title="Value Division"
      subtitle="Touch chart to view info"
      ready={true}
      expandedContent={<FullContent />}
    >
      <PreviewContent />
    </ExpandableWidget>
  );
}
