import { ActivityIndicator, View } from 'react-native';

import { useThemeColorLegacy } from '@/hooks/useThemeColor';

export default function FullScreenActivityIndicator() {
  const theme = useThemeColorLegacy();
  return (
    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: theme.backgroundPrimary }}>
      <ActivityIndicator size="small" color={theme.text} />
    </View>
  );
}
