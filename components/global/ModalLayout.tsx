import React from 'react';

import { Header, Icon } from '@rneui/themed';
import { Platform, View } from 'react-native';
import { TouchableOpacity } from 'react-native';
import { ScrollView } from 'react-native';

import { clsx } from '@/common/clsx';
import { useThemeColorLegacy } from '@/hooks/useThemeColor';

import { Text } from '../base';

type Props = { dismiss: () => void; children: React.ReactNode; title: string; noScrollView?: boolean };

export default function ModalLayout({ dismiss, children, title, noScrollView = false }: Props) {
  const theme = useThemeColorLegacy();
  return (
    <View
      style={{ flex: 1, backgroundColor: theme.backgroundPrimary }}
      className={clsx(Platform.OS === 'ios' && 'pb-5')}
    >
      <Header
        backgroundColor={theme.backgroundPrimary}
        centerComponent={
          <View className="flex-1 flex-row items-center">
            <Text className="font-bold text-[16px] text-center">{title}</Text>
          </View>
        }
        rightComponent={
          <TouchableOpacity onPress={dismiss}>
            <View className="rounded-2xl p-1 m-[5px]" style={{ backgroundColor: theme.backgroundSecondary }}>
              <Icon name="close" size={16} color={theme.muted} />
            </View>
          </TouchableOpacity>
        }
        containerStyle={{ borderBottomWidth: 0 }}
      />

      {!noScrollView ? (
        <ScrollView>
          <View className="p-5">{children}</View>
        </ScrollView>
      ) : (
        <View className="flex-1">{children}</View>
      )}
    </View>
  );
}
