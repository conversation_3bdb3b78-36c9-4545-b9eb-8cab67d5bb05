import React from 'react';

import { SafeAreaView as NativeSafeAreaView, SafeAreaViewProps } from 'react-native-safe-area-context';

import { useThemeColorLegacy } from '@/hooks/useThemeColor';

import { ThemedView } from '../global/ThemedView';

type Props = SafeAreaViewProps;

export function SafeAreaView({ children, style, ...props }: Props) {
  const theme = useThemeColorLegacy();

  return (
    <NativeSafeAreaView
      {...props}
      style={[
        {
          flex: 1,
        },
        style,
      ]}
      edges={['top', 'bottom']}
    >
      <ThemedView style={{ flex: 1 }}>{children}</ThemedView>
    </NativeSafeAreaView>
  );
}
