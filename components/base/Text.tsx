import React from 'react';

import { Text as NativeText, TextProps } from 'react-native';
import Animated from 'react-native-reanimated';

import { clsx } from '@/common/clsx';
import { useThemeColorDirect } from '@/hooks/useThemeColor';

type Props = TextProps & {
  children?: React.ReactNode;
  className?: string;
  type?: keyof typeof typeStyles;
  h1?: boolean;
  h2?: boolean;
  h3?: boolean;
  h4?: boolean;
  animated?: boolean;
};

export function Text({ children, className, type = 'default', h1, h2, h3, h4, animated, ...props }: Props) {
  const TextComponent = animated ? Animated.Text : NativeText;

  // Get theme colors
  const textColor = useThemeColorDirect('text');
  const mutedColor = useThemeColorDirect('tabIconDefault');

  // Define colors based on type
  const getTextColor = () => {
    switch (type) {
      case 'muted':
        return mutedColor;
      case 'success':
        return '#22c55e'; // green-500
      case 'error':
      case 'danger':
        return '#ef4444'; // red-500
      case 'info':
        return '#3b82f6'; // blue-500
      default:
        return textColor;
    }
  };

  return (
    <TextComponent
      {...props}
      style={[
        {
          color: getTextColor(),
          fontSize: h1 ? 28 : h2 ? 24 : h3 ? 20 : h4 ? 16 : undefined,
          fontWeight: h1 ? 'bold' : h2 ? '600' : h3 || h4 ? '500' : 'normal',
        },
        props.style,
      ]}
      className={clsx(className)}
    >
      {children}
    </TextComponent>
  );
}
